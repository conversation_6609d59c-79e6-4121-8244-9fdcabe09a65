import React from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { PatientProvider } from './contexts/PatientContext';
import { AuthProvider } from './contexts/AuthContext';

// Pages
import WelcomePage from './pages/WelcomePage';
import PatientDetailsPage from './pages/PatientDetailsPage';
import ChatPage from './pages/ChatPage';
import ThankYouPage from './pages/ThankYouPage';
import LoginPage from './pages/LoginPage';
import RegisterPage from './pages/RegisterPage';
import Layout from './components/Layout';
import ProtectedRoute from './components/ProtectedRoute';
import NotificationDemo from './components/NotificationDemo';

function App() {
  try {
    return (
      <AuthProvider>
        <PatientProvider>
          <Router>
            <Routes>
              {/* Public Routes */}
              <Route path="/login" element={<LoginPage />} />
              <Route path="/register" element={<RegisterPage />} />

              {/* Default route redirects to login */}
              <Route path="/" element={<Navigate to="/login" replace />} />

              {/* Protected Routes */}
              <Route path="/home" element={
                <ProtectedRoute>
                  <Layout>
                    <WelcomePage />
                  </Layout>
                </ProtectedRoute>
              } />
              <Route path="/patient-details" element={
                <ProtectedRoute>
                  <Layout>
                    <PatientDetailsPage />
                  </Layout>
                </ProtectedRoute>
              } />
              <Route path="/chat" element={
                <ProtectedRoute>
                  <Layout>
                    <ChatPage />
                  </Layout>
                </ProtectedRoute>
              } />
              <Route path="/thank-you" element={
                <ProtectedRoute>
                  <Layout>
                    <ThankYouPage />
                  </Layout>
                </ProtectedRoute>
              } />
            </Routes>
            {/* Notification Demo - Available on all protected routes */}
            <NotificationDemo />
          </Router>
        </PatientProvider>
      </AuthProvider>
    );
  } catch (error) {
    console.error('App Error:', error);
    return (
      <div className="min-h-screen bg-red-50 flex items-center justify-center">
        <div className="bg-white rounded-lg p-8 shadow-lg max-w-md">
          <h1 className="text-2xl font-bold text-red-800 mb-4">Application Error</h1>
          <p className="text-red-600 mb-4">Something went wrong loading the application.</p>
          <pre className="text-sm text-gray-600 bg-gray-100 p-2 rounded overflow-auto">
            {error instanceof Error ? error.message : 'Unknown error'}
          </pre>
          <button
            onClick={() => window.location.reload()}
            className="mt-4 bg-red-600 text-white px-4 py-2 rounded hover:bg-red-700"
          >
            Reload App
          </button>
        </div>
      </div>
    );
  }
}

export default App