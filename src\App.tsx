import React from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { PatientProvider } from './contexts/PatientContext';
import { AuthProvider } from './contexts/AuthContext';

// Pages
import WelcomePage from './pages/WelcomePage';
import PatientDetailsPage from './pages/PatientDetailsPage';
import ChatPage from './pages/ChatPage';
import ThankYouPage from './pages/ThankYouPage';
import LoginPage from './pages/LoginPage';
import RegisterPage from './pages/RegisterPage';
import Layout from './components/Layout';
import ProtectedRoute from './components/ProtectedRoute';
import NotificationDemo from './components/NotificationDemo';

function App() {
  // Temporary debug version to check if app loads
  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center">
      <div className="bg-white rounded-lg p-8 shadow-lg">
        <h1 className="text-2xl font-bold text-gray-800 mb-4">HealthyStream Debug</h1>
        <p className="text-gray-600">App is loading successfully!</p>
        <button
          onClick={() => window.location.reload()}
          className="mt-4 bg-indigo-600 text-white px-4 py-2 rounded hover:bg-indigo-700"
        >
          Reload App
        </button>
      </div>
    </div>
  );
}

export default App