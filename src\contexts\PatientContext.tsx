import React, { createContext, useState, useContext, ReactNode, useEffect } from 'react';
import { useAuth } from './AuthContext';
import { userDataService } from '../services/userDataService';

type AppointmentType = 'Initial' | 'Follow-Up' | 'Consultation';

interface PatientContextType {
  // Patient Details
  patientName: string;
  setPatientName: (name: string) => void;
  age: string;
  setAge: (age: string) => void;
  gender: string;
  setGender: (gender: string) => void;
  medicalHistory: string;
  setMedicalHistory: (history: string) => void;
  currentSymptoms: string;
  setCurrentSymptoms: (symptoms: string) => void;
  appointmentDate: string;
  setAppointmentDate: (date: string) => void;
  appointmentTime: string;
  setAppointmentTime: (time: string) => void;
  isAppointmentScheduled: boolean;
  setIsAppointmentScheduled: (scheduled: boolean) => void;

  // Chat
  chatHistory: Array<{ role: 'user' | 'assistant' | 'system'; content: string; timestamp?: string }>;
  addMessage: (role: 'user' | 'assistant' | 'system', content: string) => void;
  clearChat: () => void;
}

const PatientContext = createContext<PatientContextType | undefined>(undefined);

export function PatientProvider({ children }: { children: ReactNode }) {
  const { user, userSession, isNewUser } = useAuth();

  // Patient Details State
  const [patientName, setPatientName] = useState<string>('');
  const [age, setAge] = useState<string>('');
  const [gender, setGender] = useState<string>('');
  const [medicalHistory, setMedicalHistory] = useState<string>('');
  const [currentSymptoms, setCurrentSymptoms] = useState<string>('');
  const [appointmentDate, setAppointmentDate] = useState<string>('');
  const [appointmentTime, setAppointmentTime] = useState<string>('');
  const [isAppointmentScheduled, setIsAppointmentScheduled] = useState<boolean>(false);

  // Chat State
  const [chatHistory, setChatHistory] = useState<Array<{ role: 'user' | 'assistant' | 'system'; content: string; timestamp?: string }>>([]);

  const [isDataLoaded, setIsDataLoaded] = useState(false);

  // Load user data when user logs in
  useEffect(() => {
    if (user && userSession && !isDataLoaded) {
      if (isNewUser) {
        // New user - start with fresh data
        console.log('🆕 New user - starting with fresh data');
        setPatientName(user.name || '');
        setAge('');
        setGender('');
        setMedicalHistory('');
        setCurrentSymptoms('');
        setAppointmentDate('');
        setAppointmentTime('');
        setIsAppointmentScheduled(false);
        setChatHistory([]);
      } else {
        // Returning user - restore all data
        console.log('🔄 Returning user - restoring data');
        const patientDetails = userSession.patientDetails;
        setPatientName(user.name || '');
        setAge(patientDetails.age || '');
        setGender(patientDetails.gender || '');
        setMedicalHistory(patientDetails.medicalHistory || '');
        setCurrentSymptoms(patientDetails.currentSymptoms || '');
        setAppointmentDate(patientDetails.appointmentDate || '');
        setAppointmentTime(patientDetails.appointmentTime || '');
        setIsAppointmentScheduled(patientDetails.isAppointmentScheduled || false);

        // Restore chat history
        const formattedChatHistory = userSession.chatHistory.map(msg => ({
          role: msg.role,
          content: msg.content,
          timestamp: msg.timestamp
        }));
        setChatHistory(formattedChatHistory);

        console.log('📊 Restored:', {
          chatMessages: formattedChatHistory.length,
          appointmentScheduled: patientDetails.isAppointmentScheduled,
          appointmentDate: patientDetails.appointmentDate
        });
      }
      setIsDataLoaded(true);
    }
  }, [user, userSession, isNewUser, isDataLoaded]);

  // Auto-save data whenever any data changes
  useEffect(() => {
    if (user && isDataLoaded) {
      userDataService.updatePatientDetails({
        age,
        gender,
        medicalHistory,
        currentSymptoms,
        appointmentDate,
        appointmentTime,
        isAppointmentScheduled
      });
    }
  }, [user, isDataLoaded, age, gender, medicalHistory, currentSymptoms, appointmentDate, appointmentTime, isAppointmentScheduled]);

  // Methods
  const addMessage = (role: 'user' | 'assistant' | 'system', content: string) => {
    const newMessage = { role, content };
    setChatHistory(prev => [...prev, newMessage]);

    // Save to user data service
    if (user) {
      userDataService.updateChatHistory(newMessage);
    }
  };

  const clearChat = () => {
    setChatHistory([]);
    if (user) {
      userDataService.updateUserSession({ chatHistory: [] });
    }
  };

  // Reset data when user changes or logs out
  useEffect(() => {
    if (!user) {
      setPatientName('');
      setAge('');
      setGender('');
      setMedicalHistory('');
      setCurrentSymptoms('');
      setAppointmentDate('');
      setAppointmentTime('');
      setIsAppointmentScheduled(false);
      setChatHistory([]);
      setIsDataLoaded(false);
    } else {
      // Reset isDataLoaded when user changes to ensure fresh data loading
      setIsDataLoaded(false);
    }
  }, [user?.sessionId]); // Watch for user session changes

  const value = {
    patientName,
    setPatientName,
    age,
    setAge,
    gender,
    setGender,
    medicalHistory,
    setMedicalHistory,
    currentSymptoms,
    setCurrentSymptoms,
    appointmentDate,
    setAppointmentDate,
    appointmentTime,
    setAppointmentTime,
    isAppointmentScheduled,
    setIsAppointmentScheduled,
    chatHistory,
    addMessage,
    clearChat,
  };

  return (
    <PatientContext.Provider value={value}>
      {children}
    </PatientContext.Provider>
  );
}

export function usePatient() {
  const context = useContext(PatientContext);
  if (context === undefined) {
    throw new Error('usePatient must be used within a PatientProvider');
  }
  return context;
}