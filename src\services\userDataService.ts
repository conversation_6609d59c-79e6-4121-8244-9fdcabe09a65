// User Data Persistence Service - Saves and restores complete user sessions

export interface UserSession {
  email: string;
  name: string;
  code: string;
  patientDetails: {
    age: string;
    gender: string;
    medicalHistory: string;
    currentSymptoms: string;
    appointmentDate: string;
    appointmentTime: string;
    isAppointmentScheduled: boolean;
  };
  chatHistory: Array<{
    role: 'user' | 'assistant' | 'system';
    content: string;
    timestamp: string;
  }>;
  avatar?: string;
  lastLoginTime: string;
  sessionId: string;
}

export class UserDataService {
  private static instance: UserDataService;
  private readonly STORAGE_PREFIX = 'healthystream_user_';
  private readonly CURRENT_USER_KEY = 'healthystream_current_user';
  private readonly ALL_USERS_KEY = 'healthystream_all_users';

  private constructor() {}

  public static getInstance(): UserDataService {
    if (!UserDataService.instance) {
      UserDataService.instance = new UserDataService();
    }
    return UserDataService.instance;
  }

  // Generate unique session ID
  private generateSessionId(): string {
    return Date.now().toString(36) + Math.random().toString(36).substr(2);
  }

  // Create user key from email, name, and code
  private createUserKey(email: string, name: string, code: string): string {
    const normalizedEmail = email.toLowerCase().trim();
    const normalizedName = name.toLowerCase().trim();
    const normalizedCode = code.trim();
    return `${normalizedEmail}_${normalizedName}_${normalizedCode}`;
  }

  // Save complete user session
  public saveUserSession(userSession: UserSession): void {
    try {
      const userKey = this.createUserKey(userSession.email, userSession.name, userSession.code);
      const storageKey = this.STORAGE_PREFIX + userKey;
      
      // Update last login time
      userSession.lastLoginTime = new Date().toISOString();
      
      // Save individual user data
      localStorage.setItem(storageKey, JSON.stringify(userSession));
      
      // Update current user
      localStorage.setItem(this.CURRENT_USER_KEY, userKey);
      
      // Update users list
      this.updateUsersList(userKey);
      
      console.log('✅ User session saved successfully:', userKey);
    } catch (error) {
      console.error('❌ Error saving user session:', error);
    }
  }

  // Load user session by credentials
  public loadUserSession(email: string, name: string, code: string): UserSession | null {
    try {
      const userKey = this.createUserKey(email, name, code);
      const storageKey = this.STORAGE_PREFIX + userKey;
      const savedData = localStorage.getItem(storageKey);
      
      if (savedData) {
        const userSession: UserSession = JSON.parse(savedData);
        
        // Set as current user
        localStorage.setItem(this.CURRENT_USER_KEY, userKey);
        
        console.log('✅ User session loaded successfully:', userKey);
        return userSession;
      }
      
      console.log('ℹ️ No existing session found for:', userKey);
      return null;
    } catch (error) {
      console.error('❌ Error loading user session:', error);
      return null;
    }
  }

  // Get current user session
  public getCurrentUserSession(): UserSession | null {
    try {
      const currentUserKey = localStorage.getItem(this.CURRENT_USER_KEY);
      if (!currentUserKey) return null;
      
      const storageKey = this.STORAGE_PREFIX + currentUserKey;
      const savedData = localStorage.getItem(storageKey);
      
      if (savedData) {
        return JSON.parse(savedData);
      }
      
      return null;
    } catch (error) {
      console.error('❌ Error getting current user session:', error);
      return null;
    }
  }

  // Update existing user session
  public updateUserSession(updates: Partial<UserSession>): void {
    try {
      const currentSession = this.getCurrentUserSession();
      if (!currentSession) return;
      
      const updatedSession: UserSession = {
        ...currentSession,
        ...updates,
        lastLoginTime: new Date().toISOString()
      };
      
      this.saveUserSession(updatedSession);
    } catch (error) {
      console.error('❌ Error updating user session:', error);
    }
  }

  // Update chat history
  public updateChatHistory(newMessage: { role: 'user' | 'assistant' | 'system'; content: string }): void {
    try {
      const currentSession = this.getCurrentUserSession();
      if (!currentSession) return;
      
      const messageWithTimestamp = {
        ...newMessage,
        timestamp: new Date().toISOString()
      };
      
      const updatedChatHistory = [...currentSession.chatHistory, messageWithTimestamp];
      
      this.updateUserSession({
        chatHistory: updatedChatHistory
      });
    } catch (error) {
      console.error('❌ Error updating chat history:', error);
    }
  }

  // Update patient details
  public updatePatientDetails(patientDetails: Partial<UserSession['patientDetails']>): void {
    try {
      const currentSession = this.getCurrentUserSession();
      if (!currentSession) return;
      
      const updatedPatientDetails = {
        ...currentSession.patientDetails,
        ...patientDetails
      };
      
      this.updateUserSession({
        patientDetails: updatedPatientDetails
      });
    } catch (error) {
      console.error('❌ Error updating patient details:', error);
    }
  }

  // Create new user session
  public createNewUserSession(email: string, name: string, code: string, avatar?: string): UserSession {
    const newSession: UserSession = {
      email: email.trim(),
      name: name.trim(),
      code: code.trim(),
      patientDetails: {
        age: '',
        gender: '',
        medicalHistory: '',
        currentSymptoms: '',
        appointmentDate: '',
        appointmentTime: '',
        isAppointmentScheduled: false
      },
      chatHistory: [],
      avatar,
      lastLoginTime: new Date().toISOString(),
      sessionId: this.generateSessionId()
    };
    
    this.saveUserSession(newSession);
    return newSession;
  }

  // Logout current user
  public logoutCurrentUser(): void {
    try {
      localStorage.removeItem(this.CURRENT_USER_KEY);
      console.log('✅ User logged out successfully');
    } catch (error) {
      console.error('❌ Error logging out user:', error);
    }
  }

  // Clear all app data (for fresh start)
  public clearAllAppData(): void {
    try {
      // Get all keys that start with our prefix
      const keysToRemove: string[] = [];
      
      for (let i = 0; i < localStorage.length; i++) {
        const key = localStorage.key(i);
        if (key && (key.startsWith(this.STORAGE_PREFIX) || 
                   key === this.CURRENT_USER_KEY || 
                   key === this.ALL_USERS_KEY)) {
          keysToRemove.push(key);
        }
      }
      
      // Remove all app-related data
      keysToRemove.forEach(key => localStorage.removeItem(key));
      
      console.log('✅ All app data cleared successfully');
    } catch (error) {
      console.error('❌ Error clearing app data:', error);
    }
  }

  // Update users list for management
  private updateUsersList(userKey: string): void {
    try {
      const existingUsers = JSON.parse(localStorage.getItem(this.ALL_USERS_KEY) || '[]');
      if (!existingUsers.includes(userKey)) {
        existingUsers.push(userKey);
        localStorage.setItem(this.ALL_USERS_KEY, JSON.stringify(existingUsers));
      }
    } catch (error) {
      console.error('❌ Error updating users list:', error);
    }
  }

  // Get all registered users (for admin purposes)
  public getAllUsers(): string[] {
    try {
      return JSON.parse(localStorage.getItem(this.ALL_USERS_KEY) || '[]');
    } catch (error) {
      console.error('❌ Error getting all users:', error);
      return [];
    }
  }

  // Check if user exists
  public userExists(email: string, name: string, code: string): boolean {
    try {
      const userKey = this.createUserKey(email, name, code);
      const storageKey = this.STORAGE_PREFIX + userKey;
      return localStorage.getItem(storageKey) !== null;
    } catch (error) {
      console.error('❌ Error checking if user exists:', error);
      return false;
    }
  }

  // Get user statistics
  public getUserStats(): { totalUsers: number; currentUser: string | null } {
    return {
      totalUsers: this.getAllUsers().length,
      currentUser: localStorage.getItem(this.CURRENT_USER_KEY)
    };
  }
}

// Export singleton instance
export const userDataService = UserDataService.getInstance();
