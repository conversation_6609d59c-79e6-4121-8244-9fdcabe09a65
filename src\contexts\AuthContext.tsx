import React, { createContext, useContext, useState, ReactNode, useEffect } from 'react';
import { userDataService, UserSession } from '../services/userDataService';

interface UserData {
  email: string;
  name: string;
  code: string;
  avatar?: string;
  sessionId: string;
  isAuthenticated: boolean;
}

interface AuthContextType {
  user: UserData | null;
  userSession: UserSession | null;
  login: (email: string, name: string, code: string, avatar?: string) => Promise<boolean>;
  logout: () => void;
  clearAllData: () => void;
  isLoading: boolean;
  isNewUser: boolean;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const AuthProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [user, setUser] = useState<UserData | null>(null);
  const [userSession, setUserSession] = useState<UserSession | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isNewUser, setIsNewUser] = useState(false);

  // Check for existing user session on app load
  useEffect(() => {
    const checkExistingSession = () => {
      try {
        const currentSession = userDataService.getCurrentUserSession();
        if (currentSession) {
          setUser({
            email: currentSession.email,
            name: currentSession.name,
            code: currentSession.code,
            avatar: currentSession.avatar,
            sessionId: currentSession.sessionId,
            isAuthenticated: true,
          });
          setUserSession(currentSession);
          setIsNewUser(false);
          console.log('✅ Restored existing user session:', currentSession.email);
        } else {
          console.log('ℹ️ No existing session found');
        }
      } catch (error) {
        console.error('❌ Error loading user session:', error);
        userDataService.logoutCurrentUser();
      } finally {
        setIsLoading(false);
      }
    };

    checkExistingSession();
  }, []);

  const login = async (email: string, name: string, code: string, avatar?: string): Promise<boolean> => {
    try {
      setIsLoading(true);

      // Check if user exists
      const existingSession = userDataService.loadUserSession(email, name, code);

      if (existingSession) {
        // Returning user - restore all data
        setUser({
          email: existingSession.email,
          name: existingSession.name,
          code: existingSession.code,
          avatar: existingSession.avatar || avatar,
          sessionId: existingSession.sessionId,
          isAuthenticated: true,
        });
        setUserSession(existingSession);
        setIsNewUser(false);

        console.log('✅ Returning user logged in:', email);
        console.log('📊 Restored data:', {
          chatMessages: existingSession.chatHistory.length,
          appointmentScheduled: existingSession.patientDetails.isAppointmentScheduled,
          lastLogin: existingSession.lastLoginTime
        });

        return true;
      } else {
        // New user - create fresh session
        const newSession = userDataService.createNewUserSession(email, name, code, avatar);

        setUser({
          email: newSession.email,
          name: newSession.name,
          code: newSession.code,
          avatar: newSession.avatar,
          sessionId: newSession.sessionId,
          isAuthenticated: true,
        });
        setUserSession(newSession);
        setIsNewUser(true);

        console.log('✅ New user created:', email);
        console.log('🆕 Fresh session started');

        return true;
      }
    } catch (error) {
      console.error('❌ Login error:', error);
      alert('Login failed. Please try again.');
      return false;
    } finally {
      setIsLoading(false);
    }
  };

  const logout = () => {
    console.log('🔄 Starting logout process...');
    try {
      // Save current session before logout
      if (userSession) {
        userDataService.saveUserSession(userSession);
      }

      // Clear current user from service
      userDataService.logoutCurrentUser();

      // Reset all state
      setUser(null);
      setUserSession(null);
      setIsNewUser(false);
      setIsLoading(false);

      console.log('✅ Logout successful');
    } catch (error) {
      console.error('❌ Error during logout:', error);
      // Ensure state is cleared even if there's an error
      setUser(null);
      setUserSession(null);
      setIsNewUser(false);
      setIsLoading(false);
    }
  };

  const clearAllData = () => {
    console.log('🗑️ Clearing all app data...');
    try {
      // Clear all user data from storage
      userDataService.clearAllAppData();

      // Reset all state
      setUser(null);
      setUserSession(null);
      setIsNewUser(false);
      setIsLoading(false);

      console.log('✅ All app data cleared - fresh start ready');
    } catch (error) {
      console.error('❌ Error clearing app data:', error);
    }
  };

  return (
    <AuthContext.Provider value={{
      user,
      userSession,
      login,
      logout,
      clearAllData,
      isLoading,
      isNewUser
    }}>
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};
